import QtQuick                  2.3
import QtQuick.Controls         1.2
import QtQuick.Dialogs          1.2
import QtPositioning            5.3

import QGroundControl                           1.0
import QGroundControl.ScreenTools               1.0
import QGroundControl.Controls                  1.0
import QGroundControl.Palette                   1.0
import QGroundControl.Vehicle                   1.0

Rectangle {
    id:         root
    width:      dataItem.width + _margin * 5
    height:     dataItem.height + _margin * 3
    // note-zshun: 改回白色背景，避免刺眼
    color:      Qt.rgba(255, 255, 255, 0.95)   // 白色半透明背景
    radius:     6
    border.width: 2
    border.color: Qt.rgba(0, 0, 0, 0.3)  // 深色边框

    property var    _activeVehicle:                 QGroundControl.multiVehicleManager.activeVehicle



    // 数传数据获取，即飞控数据获取区域
    // 这些参数在Vehicle.h文件中查看到其他参数
    property real   _rollAngle:                     _activeVehicle ? _activeVehicle.roll.rawValue  : 0
    property real   _pitchAngle:                    _activeVehicle ? _activeVehicle.pitch.rawValue : 0
    property real   _headingAngle:                  _activeVehicle ? _activeVehicle.heading.rawValue : 0
    property real   _altitudeRelative:              _activeVehicle ? _activeVehicle.altitudeRelative.rawValue : 0
    property real   _altitudeAMSL:                  _activeVehicle ? _activeVehicle.altitudeAMSL.rawValue : 0
    property real   _altitudeAll:                   _activeVehicle ? _altitudeRelative + _altitudeAMSL : 0
    property real   _groundSpeed:                   _activeVehicle ? _activeVehicle.groundSpeed.rawValue : 0
    property real   _climbSpeed:                    _activeVehicle ? _activeVehicle.climbRate.rawValue : 0
    property real   _distanceToHome:                _activeVehicle ? _activeVehicle.distanceToHome.rawValue : 0
    // note-zshun: 添加到地面站(遥控器岸端)的距离
    property real   _distanceToGCS:                 _activeVehicle ? _activeVehicle.distanceToGCS.rawValue : 0
    property real   _batteryVoltage:                _activeVehicle && _activeVehicle.batteries.count > 0 ? _activeVehicle.batteries.get(2).voltage.rawValue : 0
    property real   _batteryCurrent:                _activeVehicle && _activeVehicle.batteries.count > 0 ? _activeVehicle.batteries.get(2).current.rawValue : 0
    property real   _batteryRemaining:              _activeVehicle && _activeVehicle.batteries.count > 0 ? _activeVehicle.batteries.get(2).percentRemaining.rawValue : 0
    // 温度相关属性 - 可以选择不同的温度传感器
    property real   _temperature1:                  _activeVehicle && _activeVehicle.temperature ? _activeVehicle.temperature.temperature1.rawValue : 0
    property real   _temperature2:                  _activeVehicle && _activeVehicle.temperature ? _activeVehicle.temperature.temperature2.rawValue : 0
    property real   _temperature3:                  _activeVehicle && _activeVehicle.temperature ? _activeVehicle.temperature.temperature3.rawValue : 0
    // GPS相关属性
    property int    _gpsSatCount:                   _activeVehicle ? _activeVehicle.gps.count.rawValue : 0
    // property real   _gpsHeading:                   _activeVehicle ? _activeVehicle.gps.courseOverGround.rawValue : 0
    property real   _gpsHeading:                    _activeVehicle ? _activeVehicle.heading.rawValue : 0
    
    // 链接质量相关属性 - 修正版
    property bool   _linkConnected:                 _activeVehicle ? true : false  // 如果有活动飞控，则认为已连接
    property int    _linkSignalQuality:             _activeVehicle && _activeVehicle.linkStatus ? _activeVehicle.linkStatus.signalQuality : 0
    property int    _linkSignalStrength:            _activeVehicle && _activeVehicle.linkStatus ? _activeVehicle.linkStatus.signalStrength : 0



    property var    activeVehicleCoordinate:        _activeVehicle ? _activeVehicle.coordinate : QtPositioning.coordinate()

    // 转换函数
    // 将十进制度转换为度分秒格式
    function formatDegreeMinuteSecond(decimalDegree, isLongitude) {
        if (isNaN(decimalDegree)) return "--°--'--\""

        var degree = Math.floor(Math.abs(decimalDegree))
        var minute = Math.floor((Math.abs(decimalDegree) - degree) * 60)
        var second = ((Math.abs(decimalDegree) - degree) * 60 - minute) * 60

        var direction = ""
        if (isLongitude) {
            direction = decimalDegree >= 0 ? "E" : "W"
        } else {
            direction = decimalDegree >= 0 ? "N" : "S"
        }

        return degree + "°" + minute.toString().padStart(2, '0') + "'" + second.toFixed(1).padStart(4, '0') + "\"" + direction
    }

    // 将m/s转换为节（knots）
    // 1 m/s = 1.943844 knots
    function convertToKnots(speedMs) {
        if (isNaN(speedMs)) return 0
        return speedMs * 1.943844
    }

    // note-zshun: 改回黑色字体配白色背景
    property color _nameColor:                      Qt.rgba(0, 0, 0, 0.9)   // 黑色字体
    property color _valueColor:                     Qt.rgba(0, 0, 0, 0.8)   // 黑色字体，稍微淡一点

    // 创建局部变量，数据的宽度和文本之间的宽度，推荐以下这些写法，只用修改一处，别的地方也会生效
    // 参数名的宽度
    property real  _nameWidth:                      ScreenTools.defaultFontPixelWidth * 8
    property real  _valueWidth:                     ScreenTools.defaultFontPixelWidth * 11
    property real  _margin:                         ScreenTools.defaultFontPixelWidth * 1
    // 增大字体大小
    property real  _fontSize:                       ScreenTools.largeFontPointSize * 1.1

    MouseArea {
        anchors.fill: parent
        onClicked: {

        }
    }

    // *************** 标题栏 *****************
    Rectangle {
        id: titleBar
        width: parent.width
        height: _fontSize * 1.8
        // note-zshun: 改回浅色标题栏
        color:      Qt.rgba(245, 240, 240, 1.0)   // 浅色背景
        radius: 6
        anchors.top: parent.top

        QGCLabel {
            text: "航行数据面板"
            font.bold: true
            font.pointSize: _fontSize * 0.9
            // note-zshun: 黑色字体
            color: Qt.rgba(0, 0, 0, 0.8)
            anchors.centerIn: parent
        }
    }

    // *************** 参数区域 *****************
    Item {
        id:             dataItem
        height:         _dataColumn.height
        width:          _dataRow.width
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: titleBar.bottom
        anchors.topMargin: _margin

        // 将原来的列布局改为行布局，实现两列显示
        Row {
            id:         _dataRow
            spacing:    _margin * 3

            // 第一列数据
            Column {
                id:         _dataColumn
                spacing:    _margin

                // 速度
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "速度"
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        text:                           (_activeVehicle ? convertToKnots(_groundSpeed).toFixed(1) : "--.--") + "kn"
                        // note-zshun: 改进阳光下可见性 - 使用高对比度颜色
                        color:                          !_activeVehicle ? "#FF4444" : "#44FF44"  // 更鲜艳的红绿色
                        width:                          _valueWidth
                        anchors.verticalCenter:         parent.verticalCenter
                    }
                }

                // 航程
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "航程"
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        text:                           (_activeVehicle ? (_activeVehicle.flightDistance.value).toFixed(1) : "--.--") + "m"
                        // note-zshun: 改进阳光下可见性 - 使用高对比度颜色
                        color:                          !_activeVehicle ? "#FF4444" : "#44FF44"  // 更鲜艳的红绿色
                        width:                          _valueWidth
                        anchors.verticalCenter:         parent.verticalCenter
                    }
                }

                // note-zshun: 修改为显示到地面站(遥控器岸端)的距离
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "D2GCS"  // Distance to Ground Control Station
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        // note-zshun: 使用到地面站的距离，如果无效则显示到Home的距离作为备选
                        text:                           (_activeVehicle ?
                                                        (isNaN(_distanceToGCS) ? _distanceToHome.toFixed(1) : _distanceToGCS.toFixed(1)) :
                                                        "--.--") + "m"
                        // note-zshun: 改进阳光下可见性 - 使用高对比度颜色
                        color:                          !_activeVehicle ? "#FF4444" : "#44FF44"  // 更鲜艳的红绿色
                        width:                          _valueWidth
                        anchors.verticalCenter:         parent.verticalCenter
                    }
                }

                // 电池电压
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "总电压"
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        text:                           (_activeVehicle && _activeVehicle.batteries.count > 0 ? _batteryVoltage.toFixed(2) : "--.--") + "V"
                        // note-zshun: 修改颜色逻辑，NaN或无电池时显示红色，改进阳光下可见性
                        color:                          !_activeVehicle || !(_activeVehicle.batteries.count > 0) || isNaN(_batteryVoltage) ? "#FF4444" : (_batteryVoltage < 35.0 ? "#FF4444" : "#44FF44")
                        width:                          _valueWidth
                        anchors.verticalCenter:         parent.verticalCenter
                    }
                }

                // 电池电流
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "总电流"
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        text:                           (_activeVehicle && _activeVehicle.batteries.count > 0 ? _batteryCurrent.toFixed(2) : "--.--") + "A"
                        // note-zshun: 修改颜色逻辑，NaN或无电池时显示红色，改进阳光下可见性
                        color:                          !_activeVehicle || !(_activeVehicle.batteries.count > 0) || isNaN(_batteryCurrent) ? "#FF4444" : "#44FF44"
                        width:                          _valueWidth
                        anchors.verticalCenter:         parent.verticalCenter
                    }
                }

                // 电池剩余
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "电量"
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        text:                           (_activeVehicle && _activeVehicle.batteries.count > 0 ? _batteryRemaining.toFixed(0) : "--") + "%"
                        // note-zshun: 修改颜色逻辑，NaN或无电池时显示红色，改进阳光下可见性
                        color:                          !_activeVehicle || !(_activeVehicle.batteries.count > 0) || isNaN(_batteryRemaining) ? "#FF4444" : "#44FF44"
                        width:                          _valueWidth
                        anchors.verticalCenter:         parent.verticalCenter
                    }
                }

               
                // 信号链接质量
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "链接"
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        text:                           _activeVehicle ?
                                                        (_linkSignalQuality > 0 ?
                                                            _linkSignalQuality.toString() + "%" :
                                                            "已连接") :
                                                        "未连接"
                        // note-zshun: 改进阳光下可见性 - 使用高对比度颜色
                        color:                          _activeVehicle ? "#44FF44" : "#FF4444"
                        width:                          _valueWidth
                        anchors.verticalCenter:         parent.verticalCenter
                        elide:                          Text.ElideRight  // 添加省略号处理
                    }
                }
            }

            // note-zshun: 改回灰色分隔线
            Rectangle {
                width: 1
                height: _dataColumn.height
                color: Qt.rgba(180, 180, 180, 0.6)  // 灰色分隔线
                anchors.verticalCenter: parent.verticalCenter
            }

            // 第二列数据
            Column {
                id:         _dataColumn2
                spacing:    _margin

                // 经度
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "经度"
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize * 0.85  // 稍微缩小字体以适应更长的文本
                        text:                           formatDegreeMinuteSecond(activeVehicleCoordinate.longitude, true)
                        // note-zshun: 修改颜色逻辑，GPS信号不足时显示红色，改进阳光下可见性
                        color:                          !_activeVehicle ? "#FF4444" : ((_gpsSatCount < 10) ? "#FF4444" : "#44FF44")
                        width:                          _valueWidth * 1.5  // 增加宽度以容纳度分秒格式
                        anchors.verticalCenter:         parent.verticalCenter
                        elide:                          Text.ElideRight
                    }
                }

                // 纬度
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "纬度"
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize * 0.85  // 稍微缩小字体以适应更长的文本
                        text:                           formatDegreeMinuteSecond(activeVehicleCoordinate.latitude, false)
                        // note-zshun: 修改颜色逻辑，GPS信号不足时显示红色，改进阳光下可见性
                        color:                          !_activeVehicle ? "#FF4444" : ((_gpsSatCount < 10) ? "#FF4444" : "#44FF44")
                        width:                          _valueWidth * 1.5  // 增加宽度以容纳度分秒格式
                        anchors.verticalCenter:         parent.verticalCenter
                        elide:                          Text.ElideRight
                    }
                }

                // GPS状态
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "GNSS"
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        text:                           _activeVehicle ? _activeVehicle.gps.lock.enumStringValue : "--.--"
                        // note-zshun: 改进阳光下可见性 - 使用高对比度颜色
                        color:                          !_activeVehicle ? "#FF4444" : ((_gpsSatCount < 10) ? "#FF4444" : "#44FF44")
                        width:                          _valueWidth
                        anchors.verticalCenter:         parent.verticalCenter
                    }
                }
                 // GPS卫星数量
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "卫星数"
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        text:                           _activeVehicle ? _gpsSatCount.toString() : "--"
                        // note-zshun: 改进阳光下可见性 - 使用高对比度颜色
                        color:                          !_activeVehicle ? "#FF4444" : ((_gpsSatCount < 10) ? "#FF4444" : "#44FF44")
                        width:                          _valueWidth
                        anchors.verticalCenter:         parent.verticalCenter
                    }
                }

                // GPS航向
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "航向"
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        text:                           (_activeVehicle ? _gpsHeading.toFixed(1) : "--.--") + "°"
                        color:                          !_activeVehicle ? "red" : ((_gpsSatCount < 10) ? "red" : "green")
                        width:                          _valueWidth
                        anchors.verticalCenter:         parent.verticalCenter
                    }
                }

                // 温度
                Row {
                    spacing:                            _margin

                    QGCLabel {
                        text:                           "温度"
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        color:                          _nameColor
                        anchors.verticalCenter:         parent.verticalCenter
                        width:                          _nameWidth
                    }

                    QGCLabel {
                        font.bold:                      true
                        font.pointSize:                 _fontSize
                        text:                           (_activeVehicle && _activeVehicle.temperature ? _temperature1.toFixed(1) : "--.--") + "℃"
                        // note-zshun: 改进阳光下可见性 - 使用高对比度颜色
                        color:                          !_activeVehicle ? "#FF4444" : ((_activeVehicle.temperature && _temperature1 > 65.0) ? "#FF4444" : "#44FF44")
                        width:                          _valueWidth
                        anchors.verticalCenter:         parent.verticalCenter
                    }
                }
            }
        }
    }
}

