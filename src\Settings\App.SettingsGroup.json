{"version": 1, "fileType": "FactMetaData", "QGC.MetaData.Facts": [{"name": "offlineEditingFirmwareClass", "shortDesc": "Offline editing firmware class", "type": "uint32", "enumStrings": "ArduPilot,PX4 Pro,Mavlink Generic", "enumValues": "3,12,0", "default": 3}, {"name": "offlineEditingVehicleClass", "shortDesc": "Offline editing vehicle class", "type": "uint32", "enumStrings": "Fixed Wing,Multi-Rotor,VTOL,Rover,Sub,Mavlink Generic", "enumValues": "1,2,20,10,12,0", "default": 2}, {"name": "offlineEditingCruiseSpeed", "shortDesc": "Offline editing cruise speed", "longDesc": "This value defines the default speed for calculating mission statistics for vehicles which do not support hover or VTOL vehicles in fixed wing mode. It does not modify the flight speed for a specific flight plan.", "type": "double", "default": 2.0, "min": 1.0, "units": "m/s", "decimalPlaces": 2}, {"name": "offlineEditingHoverSpeed", "shortDesc": "Offline editing hover speed", "longDesc": "This value defines the default speed for calculating mission statistics for multi-rotor vehicles or VTOL vehicle in multi-rotor mode. It does not modify the flight speed for a specific flight plan.", "type": "double", "default": 5.0, "min": 1.0, "units": "m/s", "decimalPlaces": 2}, {"name": "offlineEditingAscentSpeed", "shortDesc": "Offline editing ascent speed", "longDesc": "This value defines the ascent speed for multi-rotor vehicles for use in calculating mission duration.", "type": "double", "default": 3.0, "min": 0.1, "units": "m/s", "decimalPlaces": 2}, {"name": "offlineEditingDescentSpeed", "shortDesc": "Offline editing descent speed", "longDesc": "This value defines the cruising speed for multi-rotor vehicles for use in calculating mission duration.", "type": "double", "default": 1.0, "min": 0.1, "units": "m/s", "decimalPlaces": 2}, {"name": "batteryPercentRemainingAnnounce", "shortDesc": "Announce battery remaining percent", "longDesc": "Announce the remaining battery percent when it falls below the specified percentage.", "type": "uint32", "default": 30, "units": "%", "min": 0, "max": 100}, {"name": "defaultMissionItemAltitude", "shortDesc": "Default value for altitude", "longDesc": "This value specifies the default altitude for new items added to a mission.", "type": "double", "default": 50.0, "min": 0.0, "units": "m", "decimalPlaces": 1}, {"name": "telemetrySave", "shortDesc": "Save telemetry Log after each flight", "longDesc": "If this option is enabled a telemetry will be saved after each flight completes.", "type": "bool", "default": true}, {"name": "telemetrySaveNotArmed", "shortDesc": "Save telemetry log even if vehicle was not armed", "longDesc": "If this option is enabled a telemtry log will be saved even if vehicle was never armed.", "type": "bool", "default": false}, {"name": "audioMuted", "shortDesc": "Mute audio output", "longDesc": "If this option is enabled all audio output will be muted.", "type": "bool", "default": false}, {"name": "checkInternet", "shortDesc": "Check Internet connection", "longDesc": "Check Internet connection before accessing Internet resources.", "type": "bool", "default": true}, {"name": "virtualJoystick", "shortDesc": "Show virtual joystick", "longDesc": "If this option is enabled the virtual joystick will be shown on the Fly view.", "type": "bool", "default": false}, {"name": "virtualJoystickAutoCenterThrottle", "shortDesc": "Auto-Center Throttle", "longDesc": "If enabled the throttle stick will snap back to center when released.", "type": "bool", "default": true}, {"name": "gstDebugLevel", "shortDesc": "Video streaming debug", "longDesc": "Sets the environment variable GST_DEBUG for all pipeline elements on boot.", "type": "uint8", "enumStrings": "Disabled,<PERSON>rror,Warning,FixMe,Info,Debug,Log,Trace", "enumValues": "0,1,2,3,4,5,6,7", "default": 0, "qgcRebootRequired": true}, {"name": "useChecklist", "shortDesc": "Use preflight checklist", "longDesc": "If this option is enabled the preflight checklist will be used.", "type": "bool", "default": false}, {"name": "enforceChecklist", "shortDesc": "Preflight checklist must pass before arming", "longDesc": "If this option is enabled the preflight checklist must pass before arming.", "type": "bool", "default": false}, {"name": "appFontPointSize", "shortDesc": "Application font size", "longDesc": "The point size for the default font used.", "type": "uint32", "units": "pt", "min": 6, "max": 48, "default": 14}, {"name": "indoorPalette", "shortDesc": "Application color scheme", "longDesc": "The color scheme for the user interface.", "type": "uint32", "enumStrings": "Indoor,Outdoor", "enumValues": "1,0", "default": 0}, {"name": "showLargeCompass", "shortDesc": "Show large compass", "longDesc": "Show large compass on instrument panel", "type": "bool", "default": false}, {"name": "savePath", "shortDesc": "Application save directory", "longDesc": "Directory to which all data files  are saved/loaded from", "type": "string", "default": ""}, {"name": "userBrandImageIndoor", "shortDesc": "User-selected brand image", "longDesc": "Location in file system of user-selected brand image (indoor)", "type": "string", "default": ""}, {"name": "userBrandImageOutdoor", "shortDesc": "User-selected brand image", "longDesc": "Location in file system of user-selected brand image (outdoor)", "type": "string", "default": true}, {"name": "mapboxToken", "shortDesc": "Access token to Mapbox maps", "longDesc": "Your personal access token for Mapbox maps", "type": "string", "default": ""}, {"name": "mapboxAccount", "shortDesc": "Account name for Mapbox maps", "longDesc": "Your personal account name for Mapbox maps", "type": "string", "default": ""}, {"name": "mapboxStyle", "shortDesc": "Map style ID", "longDesc": "Map design style ID for Mapbox maps", "type": "string", "default": ""}, {"name": "esriToken", "shortDesc": "Access token to Esri maps", "longDesc": "Your personal access token for Esri maps", "type": "string", "default": ""}, {"name": "customURL", "shortDesc": "Custom Map URL", "longDesc": "URL for X Y Z map with {x} {y} {z} or {zoom} substitutions. Eg: https://basemaps.linz.govt.nz/v1/tiles/aerial/EPSG:3857/{z}/{x}/{y}.png?api=d01ev80nqcjxddfvc6amyvkk1ka", "type": "string", "default": ""}, {"name": "vworldToken", "shortDesc": "VWorld <PERSON>", "longDesc": "Your personal access token for VWorld maps", "type": "string", "default": ""}, {"name": "defaultFirmwareType", "shortDesc": "Default firmware type for flashing", "type": "uint32", "default": 12}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "shortDesc": "Stream GCS' coordinates to Autopilot", "type": "uint32", "enumStrings": "Never,Always,When in Follow Me Flight Mode", "enumValues": "0,1,2", "default": 2}, {"name": "apmStartMavlinkStreams", "shortDesc": "Request start of MAVLink telemetry streams (ArduPilot only)", "type": "bool", "default": true, "qgcRebootRequired": true}, {"name": "enableTaisync", "shortDesc": "Enable Taisync Module Support", "longDesc": "Enable Taisync Module Support", "type": "bool", "default": false}, {"name": "enableTaisyncVideo", "shortDesc": "Enable Taisync Video Support", "longDesc": "Enable Taisync Video Support", "type": "bool", "default": true}, {"name": "enableMicrohard", "shortDesc": "Enable Microhard Module Support", "longDesc": "Enable Microhard Module Support", "type": "bool", "default": false}, {"name": "qLocaleLanguage", "shortDesc": "Language", "type": "uint32", "enumStrings": "System,Azerbaijani (Azerbaijani),бълга<PERSON><PERSON>к<PERSON> (Bulgarian),中文 (Chinese),<PERSON><PERSON>s (Dutch),English,<PERSON><PERSON> (Finnish),Fr<PERSON><PERSON> (French),Deutsche (German),<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Greek), <PERSON><PERSON><PERSON><PERSON><PERSON> (Hebrew),<PERSON><PERSON> (Italian),<PERSON><PERSON>人 (Japanese),한국어 (Korean),<PERSON><PERSON> (Norwegian),<PERSON><PERSON><PERSON> (Polish),<PERSON><PERSON><PERSON><PERSON><PERSON> (Portuguese),<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Russian),<PERSON><PERSON><PERSON><PERSON><PERSON> (Spanish),<PERSON><PERSON> (Swedish),<PERSON><PERSON><PERSON> (Turkish)", "enumValues": "0,12,20,25,30,31,36,37,42,43,48,58,59,66,85,90,91,96,111,114,125", "comment": "enumValues uses Qt QLocale::Language values", "default": 0}, {"name": "disableAllPersistence", "shortDesc": "Disable all data persistence", "longDesc": "If this option is set, nothing will be saved to disk.", "type": "bool", "default": false}, {"name": "usePairing", "shortDesc": "Use Pairing", "longDesc": "Use Link Pairing.", "type": "bool", "default": false}, {"name": "saveCsvTelemetry", "shortDesc": "Save CSV Telementry Logs", "longDesc": "If this option is enabled, all Facts will be written to a CSV file with a 1 Hertz frequency.", "type": "bool", "default": false}, {"name": "firstRunPromptIdsShown", "shortDesc": "Comma separated list of first run prompt ids which have already been shown.", "type": "string", "default": ""}, {"name": "forwardMavlink", "shortDesc": "Enable mavlink forwarding", "longDesc": "Enable mavlink forwarding", "type": "bool", "default": false}, {"name": "forwardMavlinkHostName", "shortDesc": "Host name", "longDesc": "Host name to forward mavlink to. i.e: localhost:14445", "type": "string", "default": "localhost:14445"}]}