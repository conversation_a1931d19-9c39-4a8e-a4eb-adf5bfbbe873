{"version": 1, "fileType": "FactMetaData", "QGC.MetaData.Facts": [{"name": "videoSource", "shortDesc": "Video source", "longDesc": "Source for video. UDP, TCP, RTSP and UVC Cameras may be supported depending on Vehicle and ground station version.", "type": "string", "default": "RTSP"}, {"name": "udpPort", "shortDesc": "Video UDP Port", "longDesc": "UDP port to bind to for video stream.", "type": "uint16", "min": 1025, "default": 5600}, {"name": "rtspUrl", "shortDesc": "Video RTSP Url", "longDesc": "RTSP url address and port to bind to for video stream. Example: rtsp://************:554/live", "type": "string", "default": "rtsp://**************:8554/main.264"}, {"name": "tcpUrl", "shortDesc": "Video TCP Url", "longDesc": "TCP url address and port to bind to for video stream. Example: ***************:3001", "type": "string", "default": ""}, {"name": "videoSavePath", "shortDesc": "Video save directory", "longDesc": "Directory to save videos to.", "type": "string", "default": ""}, {"name": "aspectRatio", "shortDesc": "Video Aspect Ratio", "longDesc": "Video Aspect Ratio (width / height). Use 0.0 to ignore it.", "type": "float", "decimalPlaces": 6, "default": 1.777777}, {"name": "gridLines", "shortDesc": "Video Grid Lines", "longDesc": "Displays a grid overlaid over the video view.", "type": "uint32", "enumStrings": "<PERSON><PERSON>,<PERSON>", "enumValues": "1,0", "default": 0}, {"name": "videoFit", "shortDesc": "Video Display Fit", "longDesc": "Handle Video Aspect Ratio.", "type": "uint32", "enumStrings": "Fit Width,Fit Height,Stretch", "enumValues": "0,1,2", "default": 1}, {"name": "showRecControl", "shortDesc": "Show Video Record Control", "longDesc": "Show recording control in the UI.", "type": "bool", "default": true}, {"name": "recordingFormat", "shortDesc": "Video Recording Format", "longDesc": "Video recording file format.", "type": "uint32", "enumStrings": "mkv,mov,mp4", "enumValues": "0,1,2", "default": 0}, {"name": "maxVideoSize", "shortDesc": "Max Video Storage Usage", "longDesc": "Maximum amount of disk space used by video recording.", "type": "uint32", "min": 100, "units": "MB", "default": 10240, "mobileDefault": 2048}, {"name": "enableStorageLimit", "shortDesc": "Enable/Disable Limits on Storage Usage", "longDesc": "When enabled, old video files will be auto-deleted when the total size of QGC-recorded video exceeds the maximum video storage usage.", "type": "bool", "default": false, "mobileDefault": true}, {"name": "rtspTimeout", "shortDesc": "RTSP Video Timeout", "longDesc": "How long to wait before assuming RTSP link is gone.", "type": "uint32", "min": 1, "units": "s", "default": 2}, {"name": "streamEnabled", "shortDesc": "Video Stream Enabled", "longDesc": "Start/Stop Video Stream.", "type": "bool", "default": true}, {"name": "disable<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortDesc": "Video Stream Disnabled When Armed", "longDesc": "Disable Video Stream when disarmed.", "type": "bool", "default": false}, {"name": "lowLatencyMode", "shortDesc": "Tweaks video for lower latency", "longDesc": "If this option is enabled, the rtpjitterbuffer is removed and the video sink is set to assynchronous mode, reducing the latency by about 200 ms.", "type": "bool", "default": false}, {"name": "forceVideoDecoder", "shortDesc": "Force specific category of video decode", "longDesc": "Force the change of prioritization between video decode methods, allowing the user to force some video hardware decode plugins if necessary.", "type": "uint32", "enumStrings": "<PERSON><PERSON><PERSON>,Force software decoder,Force NVIDIA decoder,Force VA-API decoder,Force DirectX3D 11 decoder,Force VideoToolbox decoder", "enumValues": "0,1,2,3,4,5", "default": 0, "qgcRebootRequired": true}]}